/*
 * Copyright(c) RIB Software GmbH
 */


import {
    IEntityList,
    IEntityRuntimeDataRegistry,
    IValidationFunctions,
    ValidationInfo,
    ValidationResult
} from '@libs/platform/data-access';
import { IEntityIdentification } from '@libs/platform/common';
import { SalesBaseValidationService } from '../../base/sales-base-validation.service';


/**
 * Sales Common Validation Service
 * Migrated from sales-common-validation-service-provider.js
 * Provides validation services for common sales modules
 */
export abstract class SalesCommonValidationService<T extends IEntityIdentification> extends SalesBaseValidationService<T> {

    protected constructor(
        protected readonly dataService: IEntityList<T> & IEntityRuntimeDataRegistry<T>
    ) {
        super();
    }

    /**
     * Override to provide time span information for date validation
     */
    protected override getMissingTimeSpanInfo = (info: ValidationInfo<T>): ValidationInfo<T> | undefined => {
        const entity = info.entity as unknown as Record<string, Date | string | number | undefined | null>;
        switch (info.field) {
            case 'PlannedStart':
                return new ValidationInfo(info.entity, entity['PlannedEnd'] as Date | string | number | undefined, 'PlannedEnd');
            case 'PlannedEnd':
                return new ValidationInfo(info.entity, entity['PlannedStart'] as Date | string | number | undefined, 'PlannedStart');
            default:
                return undefined;
        }
    };

    protected generateValidationFunctions(): IValidationFunctions<T> {
        return {
            Code: this.validateCode.bind(this),
            PlannedStart: this.validatePlannedStart.bind(this),
            PlannedEnd: this.validatePlannedEnd.bind(this),
            LanguageFk: this.validateLanguageFk.bind(this),
            BusinesspartnerFk: this.validateBusinesspartnerFk.bind(this),
            SubsidiaryFk: this.validateSubsidiaryFk.bind(this),
            CustomerFk: this.validateCustomerFk.bind(this)
        };
    }

    protected getEntityRuntimeData(): IEntityRuntimeDataRegistry<T> {
        return this.dataService;
    }

    /**
     * Validates mandatory unique entity codes
     */
    protected validateCode(info: ValidationInfo<T>): ValidationResult {
        // Use the new BaseValidationService methods
        const mandatoryResult = this.validateIsMandatory(info);
        if (!mandatoryResult.valid) {
            return mandatoryResult;
        }
        return this.validateIsLocalUnique(info);
    }

    /**
     * Validates planned start date against planned end date
     */
    protected validatePlannedStart(info: ValidationInfo<T>): ValidationResult {
        // Use BaseValidationService method for time span validation
        return this.validateIsValidTimeSpanFrom(info);
    }

    /**
     * Validates planned end date against planned start date
     */
    protected validatePlannedEnd(info: ValidationInfo<T>): ValidationResult {
        // Use BaseValidationService method for time span validation
        return this.validateIsValidTimeSpanTo(info);
    }

    /**
     * Validates language foreign key (handles 0 as undefined)
     */
    protected validateLanguageFk(info: ValidationInfo<T>): ValidationResult {
        // Handle 0 Id as undefined
        const languageId = info.value === 0 ? undefined : info.value;
        const modifiedInfo = new ValidationInfo(info.entity, languageId as number | undefined, info.field);

        return this.validateIsMandatory(modifiedInfo);
    }

    /**
     * Validates business partner foreign key
     * Includes readonly logic for subsidiary field
     */
    protected validateBusinesspartnerFk(info: ValidationInfo<T>): ValidationResult {
        // Handle 0 Id as undefined
        const businesspartnerId = info.value === 0 ? undefined : info.value;
        const modifiedInfo = new ValidationInfo(info.entity, businesspartnerId as number | undefined, info.field);

        const result = this.validateIsMandatory(modifiedInfo);

        // Set subsidiary to readonly if no BP is set
        // TODO: Implement runtime data service readonly functionality
        // This requires integration with platform runtime data service
        // Original: platformRuntimeDataService.readonly(entity, [{field: 'SubsidiaryFk', readonly: businesspartnerId === null}]);

        return result;
    }

    /**
     * Validates subsidiary foreign key
     */
    protected validateSubsidiaryFk(info: ValidationInfo<T>): ValidationResult {
        return this.validateIsMandatory(info);
    }

    /**
     * Validates customer foreign key (handles 0 as undefined)
     */
    protected validateCustomerFk(_info: ValidationInfo<T>): ValidationResult {
        // Always return success for customer validation (as per original logic)
        return this.createSuccessResult();
    }

    // TODO: Implement async validation methods
    // These require complex service integrations that are not yet available in the new Angular architecture

    /**
     * TODO: Async validation for business partner foreign key
     * Requires: salesCommonBusinesspartnerSubsidiaryCustomerService integration
     */
    protected async asyncValidateBusinesspartnerFk(_info: ValidationInfo<T>): Promise<ValidationResult> {
        // TODO: Implement async business partner validation with related value population
        // This method requires integration with salesCommonBusinesspartnerSubsidiaryCustomerService
        // and complex logic for VAT group handling and recalculation
        return this.createSuccessResult();
    }

    /**
     * TODO: Async validation for customer foreign key
     * Requires: salesCommonBusinesspartnerSubsidiaryCustomerService integration
     */
    protected async asyncValidateCustomerFk(_info: ValidationInfo<T>): Promise<ValidationResult> {
        // TODO: Implement async customer validation with related value population
        // This method requires integration with salesCommonBusinesspartnerSubsidiaryCustomerService
        return this.createSuccessResult();
    }

    /**
     * TODO: Async validation for bill-to business partner foreign key
     * Requires: salesCommonBusinesspartnerSubsidiaryCustomerService integration
     */
    protected async asyncValidateBusinesspartnerBilltoFk(_info: ValidationInfo<T>): Promise<ValidationResult> {
        // TODO: Implement async bill-to business partner validation
        return this.createSuccessResult();
    }

    /**
     * TODO: Async validation for bill-to customer foreign key
     * Requires: salesCommonBusinesspartnerSubsidiaryCustomerService integration
     */
    protected async asyncValidateCustomerBilltoFk(_info: ValidationInfo<T>): Promise<ValidationResult> {
        // TODO: Implement async bill-to customer validation
        return this.createSuccessResult();
    }

    /**
     * Async validation for company period dates
     * Validates date against company posting periods
     */
    protected async asyncValidateDateForCompanyPeriod(info: ValidationInfo<T>): Promise<ValidationResult> {
        const url = 'sales/common/validateDateForCompanyPeriod';
        const data = {
            newValue: info.value
        };

        try {
            const response = await this.http.post<{validationNumber: number, period: string}>(url, data);

            if (!response) {
                return this.createSuccessResult();
            }

            const validationNumber = response.validationNumber;
            const period = response.period;
            let errorKey = '';

            switch (validationNumber) {
                case 1:
                    errorKey = 'sales.billing.errorNoPostingPeriod';
                    break;
                case 2:
                    errorKey = 'sales.billing.errorNotInPeriod';
                    break;
                case 3:
                    errorKey = 'sales.billing.errorPeriodNotOpen';
                    break;
                default:
                    return this.createSuccessResult();
            }

            const translationResult = this.translationService.instant(errorKey, { period });
            const errorMessage = translationResult.text;
            return new ValidationResult(errorMessage);

        } catch (error) {
            // In case of HTTP error, return success to avoid blocking the UI
            return this.createSuccessResult();
        }
    }

    /**
     * Helper method to handle zero values as undefined
     * Common pattern in the original service
     */
    protected handleZeroAsUndefined(value: number | string | boolean | object | null | undefined): number | string | boolean | object | undefined {
        return value === 0 ? undefined : value;
    }

    /**
     * Helper method to check if entity has specific properties
     * Used for type-safe property access
     */
    protected hasProperty(entity: T, property: string): boolean {
        return property in entity;
    }

}

/**
 * Concrete implementation of SalesCommonValidationService
 * Used by the factory method
 */
class SalesCommonValidationServiceImpl<T extends IEntityIdentification> extends SalesCommonValidationService<T> {
    constructor(dataService: IEntityList<T> & IEntityRuntimeDataRegistry<T>) {
        super(dataService);
    }
}

/**
 * Factory function to create validation service instances
 * Follows the pattern from the original AngularJS service
 */
export function createSalesCommonValidationService<T extends IEntityIdentification>(
    dataService: IEntityList<T> & IEntityRuntimeDataRegistry<T>
): SalesCommonValidationService<T> {
    return new SalesCommonValidationServiceImpl(dataService);
}
