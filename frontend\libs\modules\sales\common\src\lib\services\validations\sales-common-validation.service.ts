/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable } from '@angular/core';
import {
    IEntityList,
    IEntityRuntimeDataRegistry,
    IValidationFunctions,
    ValidationInfo,
    ValidationResult
} from '@libs/platform/data-access';
import { IEntityIdentification, PlatformHttpService, PropertyPath } from '@libs/platform/common';
import { SalesBaseValidationService } from '../../base/sales-base-validation.service';
import { HttpParams } from '@angular/common/http';

/**
 * Sales Common Validation Service
 * Migrated from sales-common-validation-service-provider.js
 * Provides validation services for common sales modules
 */
@Injectable({
    providedIn: 'root'
})
export class SalesCommonValidationService<T extends IEntityIdentification> extends SalesBaseValidationService<T> {
    
    protected constructor(
        protected readonly dataService: IEntityList<T> & IEntityRuntimeDataRegistry<T>
    ) {
        super();
    }

    protected generateValidationFunctions(): IValidationFunctions<T> {
        return {
            Code: this.validateCode.bind(this),
            PlannedStart: this.validatePlannedStart.bind(this),
            PlannedEnd: this.validatePlannedEnd.bind(this),
            LanguageFk: this.validateLanguageFk.bind(this),
            BusinesspartnerFk: this.validateBusinesspartnerFk.bind(this),
            SubsidiaryFk: this.validateSubsidiaryFk.bind(this),
            CustomerFk: this.validateCustomerFk.bind(this)
        };
    }

    protected getEntityRuntimeData(): IEntityRuntimeDataRegistry<T> {
        return this.dataService;
    }

    /**
     * Validates mandatory unique entity codes
     */
    protected validateCode(info: ValidationInfo<T>): ValidationResult {
        const itemList = this.dataService.getList();
        return this.validationUtils.isUniqueAndMandatory(info, itemList, 'Code');
    }

    /**
     * Validates planned start date against planned end date
     */
    protected validatePlannedStart(info: ValidationInfo<T>): ValidationResult {
        const entity = info.entity as any;
        const startDate = info.value as Date | string | number | undefined | null;
        const endDate = entity.PlannedEnd as Date | string | number | undefined | null;
        
        return this.validationUtils.validatePeriod(
            this.dataService,
            info,
            startDate,
            endDate,
            'PlannedEnd' as any
        );
    }

    /**
     * Validates planned end date against planned start date
     */
    protected validatePlannedEnd(info: ValidationInfo<T>): ValidationResult {
        const entity = info.entity as any;
        const endDate = info.value as Date | string | number | undefined | null;
        const startDate = entity.PlannedStart as Date | string | number | undefined | null;
        
        return this.validationUtils.validatePeriod(
            this.dataService,
            info,
            startDate,
            endDate,
            'PlannedStart' as any
        );
    }

    /**
     * Validates language foreign key (handles 0 as null)
     */
    protected validateLanguageFk(info: ValidationInfo<T>): ValidationResult {
        // Handle 0 Id as null
        const languageId = info.value === 0 ? null : info.value;
        const modifiedInfo = new ValidationInfo(info.entity, languageId, info.field);
        
        return this.validateIsMandatory(modifiedInfo);
    }

    /**
     * Validates business partner foreign key
     * Includes readonly logic for subsidiary field
     */
    protected validateBusinesspartnerFk(info: ValidationInfo<T>): ValidationResult {
        // Handle 0 Id as null
        const businesspartnerId = info.value === 0 ? null : info.value;
        const modifiedInfo = new ValidationInfo(info.entity, businesspartnerId, info.field);

        const result = this.validateIsMandatory(modifiedInfo);

        // Set subsidiary to readonly if no BP is set
        // TODO: Implement runtime data service readonly functionality
        // This requires integration with platform runtime data service
        // Original: platformRuntimeDataService.readonly(entity, [{field: 'SubsidiaryFk', readonly: businesspartnerId === null}]);

        return result;
    }

    /**
     * Validates subsidiary foreign key
     */
    protected validateSubsidiaryFk(info: ValidationInfo<T>): ValidationResult {
        return this.validateIsMandatory(info);
    }

    /**
     * Validates customer foreign key (handles 0 as null)
     */
    protected validateCustomerFk(info: ValidationInfo<T>): ValidationResult {
        // 0 is like null here
        const value = info.value === 0 ? null : info.value;
        
        // Always return success for customer validation (as per original logic)
        return this.createSuccessResult();
    }

    // TODO: Implement async validation methods
    // These require complex service integrations that are not yet available in the new Angular architecture

    /**
     * TODO: Async validation for business partner foreign key
     * Requires: salesCommonBusinesspartnerSubsidiaryCustomerService integration
     */
    protected async asyncValidateBusinesspartnerFk(info: ValidationInfo<T>): Promise<ValidationResult> {
        // TODO: Implement async business partner validation with related value population
        // This method requires integration with salesCommonBusinesspartnerSubsidiaryCustomerService
        // and complex logic for VAT group handling and recalculation
        return this.createSuccessResult();
    }

    /**
     * TODO: Async validation for customer foreign key
     * Requires: salesCommonBusinesspartnerSubsidiaryCustomerService integration
     */
    protected async asyncValidateCustomerFk(info: ValidationInfo<T>): Promise<ValidationResult> {
        // TODO: Implement async customer validation with related value population
        // This method requires integration with salesCommonBusinesspartnerSubsidiaryCustomerService
        return this.createSuccessResult();
    }

    /**
     * TODO: Async validation for bill-to business partner foreign key
     * Requires: salesCommonBusinesspartnerSubsidiaryCustomerService integration
     */
    protected async asyncValidateBusinesspartnerBilltoFk(info: ValidationInfo<T>): Promise<ValidationResult> {
        // TODO: Implement async bill-to business partner validation
        return this.createSuccessResult();
    }

    /**
     * TODO: Async validation for bill-to customer foreign key
     * Requires: salesCommonBusinesspartnerSubsidiaryCustomerService integration
     */
    protected async asyncValidateCustomerBilltoFk(info: ValidationInfo<T>): Promise<ValidationResult> {
        // TODO: Implement async bill-to customer validation
        return this.createSuccessResult();
    }

    /**
     * Async validation for company period dates
     * Validates date against company posting periods
     */
    protected async asyncValidateDateForCompanyPeriod(info: ValidationInfo<T>): Promise<ValidationResult> {
        const url = 'sales/common/validateDateForCompanyPeriod';
        const data = {
            newValue: info.value
        };

        try {
            const response = await this.http.post<{validationNumber: number, period: string}>(url, data).toPromise();

            if (!response) {
                return this.createSuccessResult();
            }

            const { validationNumber, period } = response;
            let errorKey = '';

            switch (validationNumber) {
                case 1:
                    errorKey = 'sales.billing.errorNoPostingPeriod';
                    break;
                case 2:
                    errorKey = 'sales.billing.errorNotInPeriod';
                    break;
                case 3:
                    errorKey = 'sales.billing.errorPeriodNotOpen';
                    break;
                default:
                    return this.createSuccessResult();
            }

            const errorMessage = await this.translationService.instant(errorKey, { period }).text;
            return new ValidationResult(errorMessage);

        } catch (error) {
            // In case of HTTP error, return success to avoid blocking the UI
            return this.createSuccessResult();
        }
    }

    /**
     * Helper method to handle zero values as null
     * Common pattern in the original service
     */
    protected handleZeroAsNull(value: any): any {
        return value === 0 ? null : value;
    }

    /**
     * Helper method to check if entity has specific properties
     * Used for type-safe property access
     */
    protected hasProperty(entity: T, property: string): boolean {
        return property in entity;
    }

    /**
     * Factory method to create validation service instances
     * Follows the pattern from the original AngularJS service
     */
    public static getInstance<T extends IEntityIdentification>(
        dataService: IEntityList<T> & IEntityRuntimeDataRegistry<T>
    ): SalesCommonValidationService<T> {
        return new SalesCommonValidationService(dataService);
    }
}
