/*
 * Copyright(c) RIB Software GmbH
 */

import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { SalesCommonValidationService } from './sales-common-validation.service';
import { ValidationInfo, ValidationResult } from '@libs/platform/data-access';
import { IEntityIdentification } from '@libs/platform/common';
import { BasicsSharedDataValidationService } from '@libs/basics/shared';

// Mock entity for testing
interface TestEntity extends IEntityIdentification {
    Id: number;
    Code?: string;
    PlannedStart?: Date;
    PlannedEnd?: Date;
    LanguageFk?: number;
    BusinesspartnerFk?: number;
    SubsidiaryFk?: number;
    CustomerFk?: number;
}

// Mock data service
class MockDataService {
    private entities: TestEntity[] = [
        { Id: 1, Code: 'TEST001' },
        { Id: 2, Code: 'TEST002' }
    ];

    getList(): TestEntity[] {
        return this.entities;
    }

    setModified(entity: TestEntity): void {
        // Mock implementation
    }
}

describe('SalesCommonValidationService', () => {
    let service: SalesCommonValidationService<TestEntity>;
    let mockDataService: MockDataService;
    let httpMock: HttpTestingController;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [HttpClientTestingModule],
            providers: [
                BasicsSharedDataValidationService,
                SalesCommonValidationService
            ]
        });

        mockDataService = new MockDataService();
        service = SalesCommonValidationService.getInstance(mockDataService as any);
        httpMock = TestBed.inject(HttpTestingController);
    });

    afterEach(() => {
        httpMock.verify();
    });

    describe('validateCode', () => {
        it('should validate unique code successfully', () => {
            const entity: TestEntity = { Id: 3, Code: 'NEW001' };
            const info = new ValidationInfo(entity, 'NEW001', 'Code');
            
            const result = service['validateCode'](info);
            
            expect(result.valid).toBe(true);
        });

        it('should fail validation for duplicate code', () => {
            const entity: TestEntity = { Id: 3, Code: 'TEST001' };
            const info = new ValidationInfo(entity, 'TEST001', 'Code');
            
            const result = service['validateCode'](info);
            
            expect(result.valid).toBe(false);
        });
    });

    describe('validateLanguageFk', () => {
        it('should handle zero as null', () => {
            const entity: TestEntity = { Id: 1, LanguageFk: 0 };
            const info = new ValidationInfo(entity, 0, 'LanguageFk');
            
            const result = service['validateLanguageFk'](info);
            
            // Should fail validation as null is not valid for mandatory field
            expect(result.valid).toBe(false);
        });

        it('should validate valid language FK', () => {
            const entity: TestEntity = { Id: 1, LanguageFk: 1 };
            const info = new ValidationInfo(entity, 1, 'LanguageFk');
            
            const result = service['validateLanguageFk'](info);
            
            expect(result.valid).toBe(true);
        });
    });

    describe('validateCustomerFk', () => {
        it('should always return success', () => {
            const entity: TestEntity = { Id: 1, CustomerFk: 0 };
            const info = new ValidationInfo(entity, 0, 'CustomerFk');
            
            const result = service['validateCustomerFk'](info);
            
            expect(result.valid).toBe(true);
        });
    });

    describe('asyncValidateDateForCompanyPeriod', () => {
        it('should handle successful validation', async () => {
            const entity: TestEntity = { Id: 1 };
            const info = new ValidationInfo(entity, new Date(), 'SomeDate');
            
            const validationPromise = service['asyncValidateDateForCompanyPeriod'](info);
            
            const req = httpMock.expectOne('sales/common/validateDateForCompanyPeriod');
            expect(req.request.method).toBe('POST');
            req.flush({ validationNumber: 0, period: '2024-01' });
            
            const result = await validationPromise;
            expect(result.valid).toBe(true);
        });

        it('should handle validation error', async () => {
            const entity: TestEntity = { Id: 1 };
            const info = new ValidationInfo(entity, new Date(), 'SomeDate');
            
            const validationPromise = service['asyncValidateDateForCompanyPeriod'](info);
            
            const req = httpMock.expectOne('sales/common/validateDateForCompanyPeriod');
            req.flush({ validationNumber: 1, period: '2024-01' });
            
            const result = await validationPromise;
            expect(result.valid).toBe(false);
        });
    });

    describe('helper methods', () => {
        it('should handle zero as null', () => {
            expect(service['handleZeroAsNull'](0)).toBeNull();
            expect(service['handleZeroAsNull'](1)).toBe(1);
            expect(service['handleZeroAsNull']('test')).toBe('test');
        });

        it('should check property existence', () => {
            const entity: TestEntity = { Id: 1, Code: 'TEST' };
            
            expect(service['hasProperty'](entity, 'Code')).toBe(true);
            expect(service['hasProperty'](entity, 'NonExistent')).toBe(false);
        });
    });

    describe('factory method', () => {
        it('should create service instance', () => {
            const instance = SalesCommonValidationService.getInstance(mockDataService as any);
            
            expect(instance).toBeInstanceOf(SalesCommonValidationService);
        });
    });
});
