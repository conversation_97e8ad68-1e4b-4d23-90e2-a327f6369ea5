# Sales Common Validation Service - Testing Guide

## Overview
This guide explains how to test the migrated `SalesCommonValidationService` in the UI and provides examples for integration testing.

## Service Migration Summary

### ✅ **Successfully Migrated Methods**
- `validateCode` - Validates mandatory unique entity codes
- `validatePlannedStart` - Validates planned start dates against end dates  
- `validatePlannedEnd` - Validates planned end dates against start dates
- `validateLanguageFk` - Validates language foreign key (handles 0 as null)
- `validateBusinesspartnerFk` - Validates business partner foreign key
- `validateSubsidiaryFk` - Validates subsidiary foreign key
- `validateCustomerFk` - Validates customer foreign key (handles 0 as null)
- `asyncValidateDateForCompanyPeriod` - Async validation for company period dates

### ⚠️ **TODO Methods (Require Additional Services)**
- `asyncValidateBusinesspartnerFk` - Requires `salesCommonBusinesspartnerSubsidiaryCustomerService`
- `asyncValidateCustomerFk` - Requires `salesCommonBusinesspartnerSubsidiaryCustomerService`
- `asyncValidateBusinesspartnerBilltoFk` - Requires service integration
- `asyncValidateCustomerBilltoFk` - Requires service integration

## Unit Testing

### Running Tests
```bash
# Run unit tests for the validation service
npm test -- --testNamePattern="SalesCommonValidationService"

# Run tests with coverage
npm test -- --coverage --testNamePattern="SalesCommonValidationService"
```

### Test Coverage
The test suite covers:
- ✅ Code uniqueness validation
- ✅ Language FK validation with zero handling
- ✅ Customer FK validation (always success)
- ✅ Async date validation with HTTP mocking
- ✅ Helper methods
- ✅ Factory method

## UI Testing

### 1. Testing in Sales Contract Module

**Location**: Sales Contract forms where validation is applied

**Test Steps**:
1. Navigate to Sales Contract creation/editing
2. Test Code field validation:
   - Enter duplicate code → Should show error
   - Enter unique code → Should validate successfully
3. Test date fields:
   - Set PlannedStart after PlannedEnd → Should show error
   - Set valid date range → Should validate successfully

**Expected Behavior**:
```typescript
// Code validation
entity.Code = "EXISTING_CODE"; // Should fail
entity.Code = "NEW_UNIQUE_CODE"; // Should pass

// Date validation  
entity.PlannedStart = new Date('2024-12-31');
entity.PlannedEnd = new Date('2024-01-01'); // Should fail - end before start
```

### 2. Testing in Sales Bid Module

**Location**: Sales Bid forms

**Test Steps**:
1. Navigate to Sales Bid creation/editing
2. Test Business Partner validation:
   - Set BusinesspartnerFk to 0 → Should handle as null
   - Set valid BusinesspartnerFk → Should validate
3. Test Language validation:
   - Set LanguageFk to 0 → Should handle as null and fail mandatory check
   - Set valid LanguageFk → Should pass

### 3. Testing Async Validation

**Location**: Any form with company period date fields

**Test Steps**:
1. Set a date field that uses `asyncValidateDateForCompanyPeriod`
2. Enter date outside posting period → Should show period error
3. Enter date in closed period → Should show period closed error
4. Enter valid date → Should validate successfully

**Expected Error Messages**:
- `sales.billing.errorNoPostingPeriod` - No posting period found
- `sales.billing.errorNotInPeriod` - Date not in period
- `sales.billing.errorPeriodNotOpen` - Period not open

## Integration Testing

### Service Usage Example

```typescript
import { SalesCommonValidationService } from '@libs/sales/common';

// In your component or service
export class SalesContractValidationService extends SalesCommonValidationService<ContractEntity> {
    
    constructor(private contractDataService: ContractDataService) {
        super(contractDataService);
    }
    
    // Use inherited validation methods
    validateContractCode(entity: ContractEntity, code: string): ValidationResult {
        const info = new ValidationInfo(entity, code, 'Code');
        return this.validateCode(info);
    }
    
    // Use async validation
    async validateContractDate(entity: ContractEntity, date: Date): Promise<ValidationResult> {
        const info = new ValidationInfo(entity, date, 'ContractDate');
        return await this.asyncValidateDateForCompanyPeriod(info);
    }
}
```

### Factory Pattern Usage

```typescript
// Create validation service instance
const validationService = SalesCommonValidationService.getInstance(dataService);

// Use validation methods
const codeValidation = validationService.validateCode(validationInfo);
const dateValidation = await validationService.asyncValidateDateForCompanyPeriod(validationInfo);
```

## Testing Checklist

### ✅ **Basic Validation Testing**
- [ ] Code uniqueness validation works
- [ ] Mandatory field validation works  
- [ ] Date period validation works
- [ ] Zero-to-null conversion works
- [ ] Factory method creates instances

### ✅ **UI Integration Testing**
- [ ] Validation errors display in forms
- [ ] Success validation clears errors
- [ ] Async validation shows loading states
- [ ] Error messages are translated correctly

### ⚠️ **TODO Testing (Future Implementation)**
- [ ] Business partner async validation
- [ ] Customer async validation  
- [ ] Bill-to validations
- [ ] Subsidiary readonly logic

## Debugging Tips

### 1. Enable Validation Logging
```typescript
// Add to validation methods for debugging
console.log('Validating:', info.field, 'with value:', info.value);
console.log('Validation result:', result);
```

### 2. Check Network Requests
- Open browser DevTools → Network tab
- Look for calls to `sales/common/validateDateForCompanyPeriod`
- Verify request payload and response

### 3. Validation Service State
```typescript
// Check if service is properly injected
console.log('Validation service:', this.validationService);
console.log('Data service:', this.dataService);
```

## Known Issues & Workarounds

### Issue 1: Async Methods Return Success
**Problem**: TODO async methods always return success
**Workaround**: Implement the missing service integrations
**Timeline**: Requires `salesCommonBusinesspartnerSubsidiaryCustomerService` migration

### Issue 2: Readonly Logic Not Implemented  
**Problem**: Subsidiary field readonly logic is not implemented
**Workaround**: Add TODO comment and implement when runtime data service is available
**Timeline**: Requires platform runtime data service integration

## Performance Considerations

- ✅ Synchronous validations are fast
- ✅ Async validations are debounced
- ⚠️ HTTP validations may need caching
- ✅ Factory pattern avoids unnecessary instances

## Migration Status

| Original Method | Status | Notes |
|----------------|--------|-------|
| validateCode | ✅ Complete | Uses `isUniqueAndMandatory` |
| validatePlannedStart/End | ✅ Complete | Uses `validatePeriod` |
| validateLanguageFk | ✅ Complete | Handles zero-to-null |
| validateBusinesspartnerFk | ⚠️ Partial | Missing readonly logic |
| validateSubsidiaryFk | ✅ Complete | Uses `validateIsMandatory` |
| validateCustomerFk | ✅ Complete | Always returns success |
| asyncValidateBusinesspartnerFk | ❌ TODO | Needs service integration |
| asyncValidateCustomerFk | ❌ TODO | Needs service integration |
| asyncValidateBusinesspartnerBilltoFk | ❌ TODO | Needs service integration |
| asyncValidateCustomerBilltoFk | ❌ TODO | Needs service integration |
| asyncValidateDateForCompanyPeriod | ✅ Complete | HTTP-based validation |
